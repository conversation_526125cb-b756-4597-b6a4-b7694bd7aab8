<?php
if (!class_exists('ErrorHandler')) {
    include(CORE_PATH . 'cake' . DS . 'libs' . DS . 'error.php');
}
class AppError extends ErrorHandler {

  function error404($params) {
    $this->controller->viewVars = array(
      'appError'         => true,
      'modifierCssClass' => 'four-zero-four',
      'hideBreadcrumb'   => true
    );

    $this->controller->layout = 'prod/default';

    // Load navigation data for 404 pages so dropdowns and mmenu work
    try {
      App::import('Component', 'Navigation');
      $navigationComponent = new NavigationComponent();
      $navigationComponent->initialize($this->controller);

      $navigationData = $navigationComponent->getNavigationData();
      if ($navigationData) {
        // Extract navigation components
        $mainNav = $navigationData['mainNav'];
        $usaDestinations = $navigationData['usaDestinations'];
        $canadaDestinations = $navigationData['canadaDestinations'];
        $holidayTypes = $navigationData['holidayTypes'];
        $whatsHot = $navigationData['whatsHot'];
        $holidayInfoPages = $navigationData['holidayInfoPages'];
        $aboutPages = $navigationData['aboutPages'];
        $farWideMenuItems = $navigationData['farWideMenuItems'];

        // Build mobile navigation
        $mobileNavigation = $navigationComponent->buildMobileNavigation();

        // Set variables for the view
        $this->controller->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'holidayTypes', 'whatsHot', 'holidayInfoPages', 'aboutPages', 'farWideMenuItems', 'mobileNavigation'));
      }
    } catch (Exception $e) {
      // Silently fail if navigation data can't be loaded - don't break the 404 page
    }

    extract($params, EXTR_OVERWRITE);

    header("HTTP/1.0 404 Not Found");
    $this->error(array('code' => '404', 'name' => 'You seem a little lost', 'message' => 'Lost'));

    $this->_stop();
  }

}
