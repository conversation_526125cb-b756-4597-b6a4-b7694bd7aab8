<?php
  if (empty($section)) {
    return;
  }

  // Count how many tabs will be rendered
  $tabCount = 1; // Always have Overview tab

  // Count related sections
  foreach ($relatedSections as $k => $v) {
    if ($v['enabled'] === true && !empty(${Inflector::variable($v['controller']) . 'Content'})) {
      $tabCount++;
    }
  }

  // Count map tab
  if (!empty($mapData) && empty($hideTheImage)) {
    $tabCount++;
  }

  // If only one tab (Overview), make it active
  $overviewActive = ($tabCount === 1) || (Inflector::underscore($this->name) == $sectionController);

  // Only show navigation if there are multiple tabs
  if ($tabCount > 1):
?>

<nav class="destinations-nav">
  <div class="destinations-nav__inner">
    <ul class="js-related-tabs">
      <li <?php if ($overviewActive) { echo 'class="active"'; } ?>>
        <button data-target="overviewContent">Overview</button>
      </li>

      <?php foreach ($relatedSections as $k => $v): ?>
        <?php if ($v['enabled'] !== true) { continue; } ?>
        <?php if (!empty(${Inflector::variable($v['controller']) . 'Content'})): ?>
          <li <?php if ($this->name == Inflector::camelize($v['controller'])) { echo 'class="active"'; } ?>>
            <button data-target="<?php echo Inflector::variable($v['controller']) . 'Content' ?>">
                <?php echo $v['label'] ?>
            </button>
          </li>
        <?php endif ?>
      <?php endforeach ?>

      <?php if (!empty($mapData) && empty($hideTheImage)): ?>
        <li>
          <button class="js-show-map">
            Map
          </button>
        </li>
      <?php endif ?>
    </ul>
  </div>
</nav>

<?php
  $javascript->codeBlock("new App.sectionTabs($$('.js-related-tabs').first(), $$('.js-related-content').first());", array('inline' => false));
  endif;
?>
