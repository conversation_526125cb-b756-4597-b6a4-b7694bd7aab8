<?php

if (empty($section)) {
    return;
}

if (empty($sectionData)) {
    $header = isset($sectionHeader) ? $sectionHeader : 'Bon Voyage';

    $theImage = isset($sectionImage) ? $sectionImage : '/img/site/placeholders/bon-voyage-offices.jpg';
} else {
    $header = $sectionData[$sectionModel]['name'];

    $theImage = $sectionData['MainImage'];
}

// Count how many sections will be rendered (same logic as section_nav.ctp)
$sectionCount = 1; // Always have Overview section

// Count related sections
foreach ($relatedSections as $k => $v) {
  if ($v['enabled'] === true && !empty(${Inflector::variable($v['controller']) . 'Content'})) {
    $sectionCount++;
  }
}

// Count map section
if (!empty($mapData) && empty($hideTheImage)) {
  $sectionCount++;
}

// Only show accordion headers if there are multiple sections
$showAccordionHeaders = ($sectionCount > 1);

?>

<div class="section-content-wrapper">
    <div
        id="overviewContent"
        class="section-content js-section-content <?php if (Inflector::underscore($this->name) == $sectionController) { echo 'active'; } ?>">
        <?php if ($showAccordionHeaders): ?>
        <button
            data-target="overviewContent"
            class="js-section-content-link section-content__link-header">
            Overview
        </button>
        <?php endif; ?>
        <?php
        if (!empty($sideBarContent)) {
            echo $sideBarContent;
        }
        ?>
        <div class="section-content__inner js-section-content-inner">
            <?php if (empty($hideTheImage)): ?>
            <div class="image-and-map">
                <div class="image-and-map__image image-and-map__image--full-image">
                    <?php
                    echo $image->image($theImage, array('version' => 'crop1000x479'));
                    ?>
                </div>
            </div>
            <?php endif ?>

            <?php if (Inflector::underscore($this->name) == $sectionController): ?>
                <?php echo $sectionContent; ?>
            <?php endif ?>
        </div>
    </div>

    <?php foreach ($relatedSections as $k => $v): ?>
        <?php if ($v['enabled'] !== true) { continue; } ?>
        <?php if (!empty(${Inflector::variable($v['controller']) . 'Content'})): ?>
            <div
                id="<?php echo Inflector::variable($v['controller']) . 'Content' ?>"
                class="section-content js-section-content <?php if ($this->name == Inflector::camelize($v['controller'])) { echo 'active'; } ?>">
                <?php if ($showAccordionHeaders): ?>
                <button
                    data-target="<?php echo Inflector::variable($v['controller']) . 'Content' ?>"
                    class="js-section-content-link section-content__link-header">
                    <?php echo $v['label'] ?>
                </button>
                <?php endif; ?>
                <div class="section-content__inner js-section-content-inner">
                    <?php echo ${Inflector::variable($v['controller']) . 'Content'}; ?>
                </div>
            </div>
        <?php endif ?>
    <?php endforeach ?>
</div>
