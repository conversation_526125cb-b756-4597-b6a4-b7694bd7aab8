<?php

class LandingPagesController extends AppController {

  var $name = 'LandingPages';
  var $components = array('Section', 'Navigation');

  function index() {

    $this->criticalCss = 'campaigns';

    $this->paginate['LandingPage'] = array(
      'limit' => 10
    );

    $landingPages = $this->paginate('LandingPage');

    $this->_canonicalUrlForPaginated();

    $breadcrumbs = array(array(
      'text' => "Campaigns",
      'url'  => $this->here
    ));

    $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/spotlights.jpg";

    $this->set(compact('landingPages', 'breadcrumbs', 'heroBannerImage'));

  }

  function view() {

    // Check if this is a Far & Wide section request
    if (isset($this->params['section']) && $this->params['section'] == 'far_wide') {
      $this->_handleFarWideView();
      return;
    }

    $related = ClassRegistry::init('HolidayType')->getHolidayTypesByLandingPage($this->sectionId);

    $landingPageActivities = ClassRegistry::init('Activity')->getActivitiesByLandingPage($this->sectionId);

    $hideBreadcrumb = true;

    $this->set(compact('landingPageActivities', 'related', 'hideBreadcrumb'));

  }

  function _handleFarWideView() {
    $slug = $this->params['landing_page_slug'];
    $parentSlug = isset($this->params['parent_slug']) ? $this->params['parent_slug'] : null;

    // Build the URL based on whether it's nested or not
    if ($parentSlug) {
      $url = '/far_wide/' . $parentSlug . '/' . $slug;
    } else {
      $url = '/far_wide/' . $slug;
    }

    // Find the navigation menu item by URL
    $navigationMenu = ClassRegistry::init('NavigationMenu')->find('first', array(
      'conditions' => array(
        'NavigationMenu.url' => $url,
        'NavigationMenu.menu_type' => 'far_wide',
        'NavigationMenu.published' => 1
      ),
      'contain' => array('LandingPage' => array('ContentBlock', 'BannerImage', 'MainImage'))
    ));

    if (empty($navigationMenu)) {
      $this->cakeError('error404');
      return;
    }

    // If a landing page is associated, use its content
    if (!empty($navigationMenu['NavigationMenu']['page_id']) && !empty($navigationMenu['LandingPage'])) {
      $landingPageId = $navigationMenu['NavigationMenu']['page_id'];

      // Load the full landing page data with images using the model's getBySlug method
      $landingPageSlug = $navigationMenu['LandingPage']['slug'];
      $landingPageData = ClassRegistry::init('LandingPage')->getBySlug($landingPageSlug);

      if (empty($landingPageData)) {
        $this->cakeError('error404');
        return;
      }

      $landingPage = $landingPageData['LandingPage'];
      $contentBlocks = $landingPageData['ContentBlock'];

      // Set up Far & Wide breadcrumbs
      $breadcrumbs = array(
        array(
          'text' => 'Far & Wide',
          'url' => false  // Non-clickable parent container
        )
      );

      // If this is a nested item, add the parent to breadcrumbs
      if ($parentSlug && !empty($navigationMenu['NavigationMenu']['parent_id'])) {
        $parentMenu = ClassRegistry::init('NavigationMenu')->findById($navigationMenu['NavigationMenu']['parent_id']);
        if (!empty($parentMenu)) {
          $parentUrl = $parentMenu['NavigationMenu']['url'];
          // Convert # URLs to false for non-clickable breadcrumbs
          if ($parentUrl === '#' || empty($parentUrl)) {
            $parentUrl = false;
          }

          $breadcrumbs[] = array(
            'text' => $parentMenu['NavigationMenu']['name'],
            'url' => $parentUrl
          );
        }
      }

      $breadcrumbs[] = array(
        'text' => $navigationMenu['NavigationMenu']['name'],
        'url' => $this->here
      );

      $this->pageTitle = $landingPage['meta_title'] ?: $navigationMenu['NavigationMenu']['name'];

      // Set up variables needed for the section layout
      $section = 'landing_pages';
      $sectionController = 'landing_pages';
      $relatedSections = array(); // No related sections for Far & Wide pages

      $this->set(compact('landingPage', 'landingPageData', 'contentBlocks', 'navigationMenu', 'breadcrumbs', 'section', 'sectionController', 'relatedSections'));
      $this->render('far_wide_page');
    } else {
      // No landing page associated, show 404 or redirect
      $this->cakeError('error404');
    }
  }

}

?>
