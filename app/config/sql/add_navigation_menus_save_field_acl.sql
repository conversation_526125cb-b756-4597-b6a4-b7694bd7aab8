-- Add webadmin_save_field ACL entry for NavigationMenus controller
-- This maintains the nested set model properly

-- Step 1: Update all nodes to the right of NavigationMenus controller
-- Shift their lft and rght values by 2 to make space for the new node
UPDATE acos SET rght = rght + 2 WHERE rght > 857;
UPDATE acos SET lft = lft + 2 WHERE lft > 857;

-- Step 2: Update the NavigationMenus controller's rght value
UPDATE acos SET rght = 859 WHERE id = 433;

-- Step 3: Insert the new webadmin_save_field action
INSERT INTO acos (parent_id, model, foreign_key, alias, lft, rght) 
VALUES (433, NULL, NULL, 'webadmin_save_field', 857, 858);

-- Verify the insertion
SELECT 'NavigationMenus controller and actions:' as info;
SELECT id, alias, lft, rght FROM acos WHERE parent_id = 433 ORDER BY lft;

SELECT 'New webadmin_save_field entry:' as info;
SELECT id, alias, lft, rght FROM acos WHERE alias = 'webadmin_save_field' AND parent_id = 433;
