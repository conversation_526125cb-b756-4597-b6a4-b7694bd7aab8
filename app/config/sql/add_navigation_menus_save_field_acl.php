<?php
/**
 * <PERSON><PERSON><PERSON> to add webadmin_save_field ACL entry for NavigationMenus controller
 * This uses CakePHP's ACL component to properly maintain the nested set model
 */

// Set up CakePHP environment
define('DS', DIRECTORY_SEPARATOR);
define('ROOT', dirname(dirname(dirname(dirname(__FILE__))))); // Go up 4 levels from app/config/sql/
define('APP_DIR', 'app');
define('CAKE_CORE_INCLUDE_PATH', ROOT . DS . 'vendor' . DS . 'cakephp' . DS . 'cakephp' . DS . 'cake');
define('WEBROOT_DIR', 'webroot');
define('WWW_ROOT', ROOT . DS . APP_DIR . DS . WEBROOT_DIR . DS);

require_once CAKE_CORE_INCLUDE_PATH . DS . 'bootstrap.php';

App::import('Component', 'Acl');
App::import('Model', 'ConnectionManager');

// Initialize ACL component
$acl = new AclComponent();

// Find the NavigationMenus controller ACO
$db = ConnectionManager::getDataSource('default');
$navigationMenusAco = $db->query("SELECT id FROM acos WHERE alias = 'NavigationMenus' AND parent_id = (SELECT id FROM acos WHERE alias = 'controllers')");

if (empty($navigationMenusAco)) {
    echo "Error: NavigationMenus controller ACO not found\n";
    exit(1);
}

$parentId = $navigationMenusAco[0][0]['id'];
echo "Found NavigationMenus controller ACO with ID: $parentId\n";

// Check if webadmin_save_field already exists
$existingAco = $db->query("SELECT id FROM acos WHERE alias = 'webadmin_save_field' AND parent_id = $parentId");

if (!empty($existingAco)) {
    echo "webadmin_save_field ACO already exists with ID: " . $existingAco[0][0]['id'] . "\n";
    exit(0);
}

// Add the webadmin_save_field ACO using CakePHP's ACL component
try {
    // Create the ACO path
    $acoPath = 'controllers/NavigationMenus/webadmin_save_field';

    echo "Adding ACO: $acoPath\n";

    // This will automatically maintain the nested set model
    $result = $acl->Aco->create(array(
        'parent_id' => $parentId,
        'alias' => 'webadmin_save_field',
        'model' => null,
        'foreign_key' => null
    ));

    if ($acl->Aco->save($result)) {
        $newId = $acl->Aco->getLastInsertId();
        echo "Successfully added webadmin_save_field ACO with ID: $newId\n";

        // Verify the nested set values were updated correctly
        $verification = $db->query("SELECT id, lft, rght FROM acos WHERE id = $newId");
        if (!empty($verification)) {
            echo "New ACO nested set values - lft: " . $verification[0][0]['lft'] . ", rght: " . $verification[0][0]['rght'] . "\n";
        }

        echo "ACL entry added successfully!\n";
    } else {
        echo "Error: Failed to save ACO\n";
        print_r($acl->Aco->validationErrors);
        exit(1);
    }

} catch (Exception $e) {
    echo "Error adding ACO: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Done!\n";
?>
